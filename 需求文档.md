# **产品需求与技术规格文档 (最终版)**

**项目代号：** Project Lighthouse-001\
**产品名称：** 动态足球数据查询工具 (Dynamic Football Stats Tool)\
**版本：** 7.0 (完整交付版)\
**日期：** 2025年7月26日\
**负责人：** (您的名字)

***

## **1. 项目概述与目标**

### **1.1 项目愿景**

本项目是“千站计划”的第一个“灯塔项目”。“千站计划”旨在通过程序化、规模化的方式，创建一系列高度专注的利基网站矩阵，以捕获海量长尾关键词流量，并通过广告实现盈利。本项目作为该计划的第一个产品，其成功将为后续所有项目提供技术模板、运营模式和商业信心。

### **1.2 项目目标**

1. **功能目标**：成功开发并上线一个功能完整的、无错误的在线工具。该工具必须能够让用户查询特定足球队在特定场景下的特定历史比赛数据（以“皇家马德里主场对阵西甲前6名球队时的黄牌数”为初始范例），并以富有洞察力的方式展示结果。

2. **业务目标**：以最小化的人力和时间成本，验证“利基工具 + 搜索引擎优化(SEO)”这一商业模式的可行性。关键衡量指标（KPIs）包括：目标关键词的搜索排名、网站独立访客数(UV)、页面浏览量(PV)以及广告千次展示收入(RPM)。

3. **用户目标**：为目标用户（特别是足彩玩家）提供一个比现有主流数据网站更快捷、更直观、更有洞察力的数据查询体验。核心是解决他们在研究特定、精细化数据时，需要手动查找和计算的痛点。

***

## **2. 目标用户画像**

### **2.1 核心用户群**

* **主要人群**: 关注欧洲五大联赛（英格兰超级联赛、西班牙甲级联赛、德国甲级联赛、意大利甲级联赛、法国甲级联赛）的资深足球迷。

* **行为特征**: 他们不仅观看比赛，还积极参与围绕比赛的分析和预测活动，例如：参与足球竞猜、体育博彩，或是玩梦幻足球（Fantasy Football）游戏。这类用户有强烈的数据需求，并且习惯于依据数据做出决策。

### **2.2 使用语言**

* 项目初期必须支持三种在全球范围内拥有大量球迷基础的语言：**英语 (en)**, **西班牙语 (es)**, **葡萄牙语 (pt)**。所有界面文本和动态生成的内容都需要支持国际化。

### **2.3 用户痛点**

* **信息过载**: 主流数据网站（如ESPN, Transfermarkt）虽然数据全面，但信息结构复杂、层级深，导致用户在寻找一个非常具体的数据点时，需要进行多次点击和页面跳转。

* **计算繁琐**: 现有工具通常只提供原始数据列表，而不提供针对特定场景的聚合统计。例如，用户想知道“某队在雨天对阵弱队时的平均角球数”，需要自己手动查找所有符合条件的比赛，并用计算器进行计算，过程繁琐且容易出错。

### **2.4 核心需求**

* 用户需要一个“一站式解答”工具。输入一个复杂、具体的查询场景，就能立即得到一个经过计算和可视化的、附有明确结论的数据洞察报告，从而高效地辅助他们做出投注或游戏决策。

***

## **3. 产品功能需求**

### **3.a. 初始页面加载与默认内容策略**

#### **3.a.1. 核心原则**

为了提供卓越的用户体验和最大化SEO效果，本网站在用户首次访问时，**必须**加载一个预先计算好的、内容丰富的默认数据报告。**严禁**向用户展示一个需要交互才能生成内容的空白页面或加载状态。

#### **3.a.2. 默认查询内容**

* **默认报告主题**: “皇家马德里在主场对阵西甲排名前6的球队时的历史平均黄牌数”。

* **选择理由**: 皇家马德里是全球最受关注的球队之一，“强强对话”是球迷和足彩玩家最关心的话题，“黄牌数”是博彩中的一个重要指标。这个默认报告能立即展示本工具的核心价值。

#### **3.a.3. 页面呈现**

1. **查询模块预设**: 当页面加载时，查询模块的五个下拉选择器必须**预先设置**为默认报告的对应值（联赛=西甲, 球队=皇家马德里, 场景=主场, 对手=对阵前6名, 数据=黄牌数）。

2. **结果模块即时显示**: 结果展示模块必须**同时加载**，并完整显示基于上述默认查询计算出的全部四个层次的信息。用户无需任何操作，即可看到一份完整的数据分析报告。

### **3.1. 页面布局与用户界面元素**

1. **头部 (Header)**：位于页面最顶部，包含网站的Logo和H1级别的网站名称。

2. **查询模块 (Query Module)**：页面核心交互区，设计为一张独立的“卡片”，有清晰的边框和背景色。

   * **动态主标题 (H1)**：根据用户选择动态生成的页面主标题。对于默认加载的页面，其标题应为“皇家马德里主场对阵强队黄牌数据统计与分析”。

   * **下拉选择器 (Dropdowns)**：一组五个可交互的下拉菜单，每个菜单前都有一个清晰的标签。

   * **操作按钮 (Button)**：一个视觉突出、颜色为主品牌色的“**查询 (Calculate)**”按钮，带有清晰的文字标签。

3. **结果展示模块 (Results Module)**：查询后动态加载显示，同样设计为一张独立的“卡片”。

4. **页脚 (Footer)**：位于页面最底部，包含版权信息（例如 © 2025 \[Your Site Name]），隐私政策和服务条款的链接，以及**强制要求的数据来源鸣谢**：“Powered by football-data.org”。

### **3.2. 核心交互流程与结果展示**

1. **首次加载 (静态生成)**: 用户访问网站URL。服务器直接返回一个完整的、包含默认报告（例如：皇马黄牌数据）的静态HTML页面。这个过程对用户来说是瞬时的。

2. **理解与探索**: 用户看到预设的查询条件和下方的完整结果，立即理解了工具的功能和用法。

3. **发起新查询 (动态获取)**: 用户可以修改查询模块中的任何一个或多个下拉框选项。当选项改变时，按钮可以变为激活状态。

4. **点击查询**: 用户点击“查询(Calculate)”按钮。按钮可以显示一个加载中的状态（例如，一个旋转的图标），以提供反馈。

5. **更新视图**: 前端向后端的API接口 (/api/get-stats) 发送一个包含新查询条件的请求。API返回新数据后，结果展示模块的全部内容被**动态更新**，以反映新的查询结果。加载状态结束。

***

## **4. 数据与技术要求**

### **4.a. API数据映射与逻辑详述**

本节为后端API (/api/get-stats.js) 的核心实现蓝图。代码必须严格遵循此流程和数据字段映射。

#### **阶段零：初始静态页面生成 (在Next.js中使用getStaticProps)**

* **触发时机**: 在网站**构建时**（或通过增量静态再生ISR定期）在服务器上执行。

* **执行流程**:

  1. 严格遵循下文 **阶段一** 和 **阶段二** 的所有步骤，使用默认查询条件（皇马、主场、对阵前6、黄牌数）。

  2. 将计算出的完整结果（一个结构化的JSON对象）作为props传递给页面组件。

  3. Next.js将使用这些props预渲染出一个完整的静态HTML文件 (index.html)。

#### **阶段一：准备工作 (适用于静态生成和动态API)**

* **静态ID**: 从football-data.org文档中获取并硬编码以下初始ID：

  * competitionId: "PD" (西班牙甲级联赛)

  * teamId: 86 (皇家马德里)

#### **阶段二：执行逻辑 (以"皇马主场对阵前6名球队的黄牌数"为例)**

* **步骤 2.1: 获取最新联赛排名，识别“前6名”对手**

  1. **API 端点**: GET https://api.football-data.org/v4/competitions/PD/standings

  2. **程序动作**: 发送请求，解析返回的JSON，定位到 standings\[0].table 数组，遍历前6个元素，提取 team.id 的值，存入 top\_6\_team\_ids 列表中。

* **步骤 2.2: 循环获取指定球队的历史比赛数据**

  1. **API 端点**: GET https://api.football-data.org/v4/teams/86/matches

  2. **程序动作**: 根据“4.3 数据时间跨度”规则，生成赛季年份列表（例如 \[2025, 2024, 2023]）。循环遍历该列表，对于每一个 season，带上查询参数 ?status=FINISHED\&competitions=PD\&season={season} 调用API。将每次返回的 matches 数组追加到 all\_matches 列表中。

* **步骤 2.3: 筛选比赛并提取所需数据**

  1. **程序动作**: 初始化变量。循环遍历 all\_matches 列表中的每一场 match 对象。通过 match.homeTeam.id 和 match.awayTeam.id 判断是否符合“主场”和“对手是前6”的条件。如果符合，则遍历 match.bookings 数组，统计 card === 'YELLOW\_CARD' 且 team.id 为86的数量，并进行累加。同时，将该场比赛的详细信息格式化后存入 detailed\_match\_list。

* **步骤 2.4: 计算最终结果并构建返回数据结构**

  1. **程序动作**: 计算平均值。执行一个简化流程计算赛季总平均值用于对比。最终打包成一个结构化的JSON对象返回。其结构**必须**如下所示：

  Generated json

  ```
  {
    "mainResult": { "averageValue": 2.8, "unit": "cards/match" },
    "comparisonResult": { "averageValue": 2.2, "unit": "cards/match" },
    "aiInsight": "In home games against top-tier opponents, Real Madrid's yellow card count is significantly higher than their season average, suggesting a more aggressive tactical approach.",
    "detailedMatches": [ { "date": "2025-10-26", "matchup": "Real Madrid CF vs FC Barcelona", "score": "3-1", "value": 5 } ],
    "queryContext": { "teamName": "Real Madrid CF", "scenario": "Home vs Top 6", "timeframe": "Seasons 2023-2026" }
  }
  ```

  content\_copydownload

  Use code [with caution](https://support.google.com/legal/answer/13505487).Json

### **4.1 数据源**

* **唯一指定数据源**: **football-data.org**。项目所有数据必须从此API获取。

### **4.2 数据安全**

* **API密钥安全**: **绝对禁止**在前端代码或任何可被用户访问的文件中暴露football-data.org的API密钥 (X-Auth-Token)。所有对外部API的请求必须在服务器端完成。

### **4.3 数据时间跨度**

* **标准规则**: 所有的数据统计和计算，必须基于 **“最近的3个已经完整结束的赛季，加上当前正在进行的赛季”** 的数据。

* **实现要求**: 程序需要能够动态判断当前日期，并自动计算出需要查询的赛季年份列表。

***

## **5. 规模化与模板化设计**

### **5.1 核心原则**

* 本项目的代码不是一次性实现，而是要作为未来创建数百个类似网站的“母版模板”。可复用性和可配置性是最高优先级的架构目标。

### **5.2 实现要求**

* **配置文件驱动**: 创建一个中心化的配置文件（例如 config.js），用于定义网站的所有可变元素，如默认查询的球队ID、联赛ID、网站名称、Logo路径等。未来创建新网站时，主要通过修改此文件来实现。

* **组件化**: 用户界面的所有部分（按钮、卡片、图表、表格）都必须被封装成独立的、可复用的React组件，并有清晰的props接口。

***

## **6. 搜索引擎优化与多语言策略**

### **6.1 搜索引擎优化 (SEO)**

* **动态元数据**: 页面的\<title>, \<h1> 和 \<meta name="description"> 标签内容必须根据用户的查询条件，在服务器端动态生成。

* **URL结构**: URL结构应当对搜索引擎友好，清晰地反映页面内容。

* **结构化数据**: 为页面添加JSON-LD格式的结构化数据（Schema.org），向搜索引擎说明页面内容是一个数据集或一个分析报告。

### **6.2 多语言支持 (Internationalization - i18n)**

* 使用成熟的i18n库（如 next-i18next）来管理翻译文本。所有静态界面文本必须从语言文件中读取，而不是硬编码在组件中。

***

## **7. 用户界面与用户体验样式指南**

### **7.1 设计指导原则**

* **数据优先**: 设计必须服务于数据，确保信息清晰、易读、主次分明。

* **充满活力**: 通过大胆的色彩和强烈的字体，营造出足球运动的激情与活力。

* **现代简洁**: 采用现代设计语言，保持界面干净整洁，避免不必要的装饰。

* **响应式设计**: 必须采用移动设备优先的策略，确保在手机、平板、桌面电脑上都有完美的用户体验。

### **7.2 色彩规范**

* **主品牌色**: #e6002e (西甲红)

* **辅助色**: #1d1d2c (深海军蓝)

* **强调色**: #ff8c00 (活力橙)

* **中性色**: #FFFFFF (主要文本), #adb5bd (次要文本), #2c2c3e (卡片背景), #495057 (边框)

### **7.3 字体排印规范**

* **字体来源**: Google Fonts。

* **标题字体**: Anton 或 Oswald。

* **正文字体**: Roboto 或 Lato。

### **7.4 核心组件样式**

* **查询按钮**: 背景色: 主品牌色, 文字颜色: #FFFFFF, 圆角: 8px。

* **下拉选择器**: 背景色: 卡片背景色, 文字颜色: #FFFFFF。

* **卡片式设计**: 所有模块有圆角和轻微阴影。

* **详细比赛表格**: 采用斑马条纹。

***

## **8. 技术栈与部署规范**

### **8.1 核心框架与语言**

* **唯一指定框架**: **Next.js**

* **唯一指定语言**: **JavaScript** (或 TypeScript)

### **8.2 渲染策略与后端逻辑实现**

* **渲染策略**: 初始页面使用 **静态站点生成 (SSG)** 并配合 **增量静态再生 (ISR)**。后续查询使用**客户端数据获取**。

* **后端API实现**: 使用Next.js内置的 **API Routes** (/pages/api/...)。

### **8.3 部署平台与流程**

* **唯一指定部署平台**: **Netlify**

* **部署流程**: 代码托管于**GitHub** -> 连接**Netlify** -> **自动化部署**。

* **环境变量**: API密钥**必须**作为环境变量存储在Netlify平台，**绝不能**写入代码。
